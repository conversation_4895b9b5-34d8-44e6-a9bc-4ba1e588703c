on:
  schedule:
    - cron: '0 15 * * *'
  workflow_dispatch:

jobs:
  azure-cleanup:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24
        cache: false

    - name: Run cleanup script
      run: |
        go run build/ci.go purge -store gethstore/builds -days 14
      env:
        AZURE_BLOBSTORE_TOKEN: ${{ secrets.AZURE_BLOBSTORE_TOKEN }}
