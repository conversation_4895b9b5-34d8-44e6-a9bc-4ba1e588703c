name: i386 linux tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: self-hosted
    steps:
    - uses: actions/checkout@v4

    # Cache build tools to avoid downloading them each time
    - uses: actions/cache@v4
      with:
        path: build/cache
        key: ${{ runner.os }}-build-tools-cache-${{ hashFiles('build/checksums.txt') }}

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.23.0
        cache: false

    - name: Run linters
      run: |
        go run build/ci.go lint
        go run build/ci.go check_generate
        go run build/ci.go check_baddeps

  build:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.0
          cache: false
      - name: Run tests
        run: go test -short ./...
        env:
          GOOS: linux
          GOARCH: 386
