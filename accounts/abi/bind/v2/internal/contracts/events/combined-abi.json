{"contracts": {"contract.sol:C": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "data", "type": "uint256"}], "name": "basic1", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bool", "name": "flag", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "data", "type": "uint256"}], "name": "basic2", "type": "event"}, {"inputs": [], "name": "EmitMulti", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "EmitOne", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bin": "6080604052348015600e575f5ffd5b506101a08061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610034575f3560e01c8063cb49374914610038578063e8e49a7114610042575b5f5ffd5b61004061004c565b005b61004a6100fd565b005b60017f8f17dc823e2f9fcdf730b8182c935574691e811e7d46399fe0ff0087795cd207600260405161007e9190610151565b60405180910390a260037f8f17dc823e2f9fcdf730b8182c935574691e811e7d46399fe0ff0087795cd20760046040516100b89190610151565b60405180910390a25f15157f3b29b9f6d15ba80d866afb3d70b7548ab1ffda3ef6e65f35f1cb05b0e2b29f4e60016040516100f39190610151565b60405180910390a2565b60017f8f17dc823e2f9fcdf730b8182c935574691e811e7d46399fe0ff0087795cd207600260405161012f9190610151565b60405180910390a2565b5f819050919050565b61014b81610139565b82525050565b5f6020820190506101645f830184610142565b9291505056fea26469706673582212207331c79de16a73a1639c4c4b3489ea78a3ed35fe62a178824f586df12672ac0564736f6c634300081c0033"}}, "version": "0.8.28+commit.7893614a.Darwin.appleclang"}